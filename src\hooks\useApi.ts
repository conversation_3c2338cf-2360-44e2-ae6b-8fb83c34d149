import { useState, useEffect, useCallback, useRef } from "react";
import { ApiError } from "@/lib/api";

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: ApiError) => void;
}

export function useApi<T>(
  apiCall: () => Promise<T>,
  options: UseApiOptions = {}
) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const hasExecutedRef = useRef(false);
  const { immediate = true, onSuccess, onError } = options;

  // Memoize the callbacks to prevent unnecessary re-renders
  const memoizedOnSuccess = useCallback((data: any) => {
    onSuccess?.(data);
  }, [onSuccess]);

  const memoizedOnError = useCallback((error: ApiError) => {
    onError?.(error);
  }, [onError]);

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await apiCall();
      setState({ data: result, loading: false, error: null });
      memoizedOnSuccess(result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof ApiError
        ? error.message
        : "An unexpected error occurred";
      setState({ data: null, loading: false, error: errorMessage });
      memoizedOnError(error as ApiError);
      throw error;
    }
  }, [apiCall, memoizedOnSuccess, memoizedOnError]);

  useEffect(() => {
    if (immediate && !hasExecutedRef.current) {
      hasExecutedRef.current = true;
      execute();
    }
  }, [immediate, execute]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
    hasExecutedRef.current = false;
  }, []);

  return {
    ...state,
    execute,
    reset,
    refetch: execute,
  };
}

export function useMutation<T, P = any>(
  mutationFn: (params: P) => Promise<T>,
  options: UseApiOptions = {}
) {
  const [state, setState] = useState<UseApiState<T> & { isIdle: boolean }>({
    data: null,
    loading: false,
    error: null,
    isIdle: true,
  });

  const mutate = useCallback(async (params: P) => {
    setState(prev => ({ ...prev, loading: true, error: null, isIdle: false }));
    
    try {
      const result = await mutationFn(params);
      setState({ data: result, loading: false, error: null, isIdle: false });
      options.onSuccess?.(result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof ApiError 
        ? error.message 
        : "An unexpected error occurred";
      setState({ data: null, loading: false, error: errorMessage, isIdle: false });
      options.onError?.(error as ApiError);
      throw error;
    }
  }, [mutationFn, options]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null, isIdle: true });
  }, []);

  return {
    ...state,
    mutate,
    reset,
  };
}

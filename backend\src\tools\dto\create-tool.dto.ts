import {
  IsString,
  IsOptional,
  IsObject,
  IsEnum,
  IsBoolean,
  IsArray,
  IsUrl,
  IsIn,
} from "class-validator";
import { ToolStatus } from "../../../../shared/types";

export class CreateToolDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsObject()
  schema: Record<string, any>;

  @IsOptional()
  @IsUrl()
  endpoint?: string;

  @IsOptional()
  @IsIn(["GET", "POST", "PUT", "PATCH", "DELETE"])
  method?: string = "POST";

  @IsOptional()
  @IsObject()
  headers?: Record<string, any> = {};

  @IsOptional()
  @IsObject()
  authentication?: Record<string, any> = {};

  @IsOptional()
  @IsEnum(ToolStatus)
  status?: ToolStatus = ToolStatus.DRAFT;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean = false;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[] = [];

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any> = {};
}

import {
  IsString,
  <PERSON><PERSON><PERSON>al,
  Is<PERSON><PERSON>ber,
  IsBoolean,
  IsArray,
  IsEnum,
  Min,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eng<PERSON>,
  IsO<PERSON>,
} from "class-validator";
import { AgentStatus } from "@prisma/client";

export class CreateAgentDto {
  @IsString()
  @MinLength(1)
  @MaxLength(255)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @IsString()
  @MinLength(1)
  prompt: string;

  @IsString()
  @MinLength(1)
  model: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number = 0.7;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(32000)
  maxTokens?: number = 1000;

  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus = AgentStatus.DRAFT;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean = false;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[] = [];

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any> = {};

  @IsOptional()
  @IsString()
  templateId?: string;
}

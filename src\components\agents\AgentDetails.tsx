"use client";

import React, { useState } from "react";
import { Agent, AgentStatus } from "../../../shared/types";
import { apiClient } from "@/lib/api";
import { useApi } from "@/hooks/useApi";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Bot,
  Edit,
  Play,
  Copy,
  Calendar,
  User,
  Settings,
  Activity,
  BarChart3,
  Clock,
  Zap,
} from "lucide-react";
import AgentExecutions from "./AgentExecutions";

interface AgentDetailsProps {
  agentId: string;
  onEdit?: (agent: Agent) => void;
  onExecute?: (agent: Agent) => void;
}

const AgentDetails = ({ agentId, onEdit, onExecute }: AgentDetailsProps) => {
  const [activeTab, setActiveTab] = useState("overview");

  const {
    data: agentResponse,
    loading,
    error,
    refetch,
  } = useApi(
    () => apiClient.getAgent(agentId),
    { immediate: true }
  );

  const agent = agentResponse?.data;

  const getStatusColor = (status: AgentStatus) => {
    switch (status) {
      case AgentStatus.ACTIVE:
        return "bg-green-100 text-green-800 border-green-200";
      case AgentStatus.DRAFT:
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case AgentStatus.ARCHIVED:
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (loading) {
    return (
      <div className="bg-white space-y-4">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5" />
                <Skeleton className="h-6 w-48" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-9 w-20" />
                <Skeleton className="h-9 w-20" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-32 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !agent) {
    return (
      <Card className="bg-white">
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading agent: {error || "Agent not found"}</p>
            <Button onClick={refetch} className="mt-2">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="bg-white space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Bot className="h-6 w-6 text-blue-500" />
              <div>
                <CardTitle className="text-xl">{agent.name}</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={getStatusColor(agent.status)}>
                    {agent.status}
                  </Badge>
                  {agent.isPublic && (
                    <Badge variant="outline">Public</Badge>
                  )}
                  <span className="text-sm text-gray-500">v{agent.version}</span>
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onEdit?.(agent)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              {agent.status === AgentStatus.ACTIVE && (
                <Button onClick={() => onExecute?.(agent)}>
                  <Play className="h-4 w-4 mr-2" />
                  Execute
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="configuration">Configuration</TabsTrigger>
              <TabsTrigger value="executions">Executions</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Activity className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">Total Executions</span>
                    </div>
                    <p className="text-2xl font-bold mt-1">
                      {(agent as any)._count?.executions || 0}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">Active Sessions</span>
                    </div>
                    <p className="text-2xl font-bold mt-1">
                      {(agent as any)._count?.sessions || 0}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4 text-purple-500" />
                      <span className="text-sm font-medium">Widgets</span>
                    </div>
                    <p className="text-2xl font-bold mt-1">
                      {(agent as any)._count?.widgets || 0}
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Description</h3>
                  <p className="text-gray-600">
                    {agent.description || "No description provided."}
                  </p>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="font-medium">Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {agent.tags && agent.tags.length > 0 ? (
                        agent.tags.map((tag) => (
                          <Badge key={tag} variant="secondary">
                            {tag}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-sm text-gray-500">No tags</span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Details</h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Created by:</span>
                      <span className="text-sm font-medium">
                        {(agent as any).user?.firstName} {(agent as any).user?.lastName}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Created:</span>
                      <span className="text-sm font-medium">
                        {new Date(agent.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Last updated:</span>
                      <span className="text-sm font-medium">
                        {new Date(agent.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Main Prompt</h3>
                <ScrollArea className="h-32 w-full border rounded-md p-3">
                  <pre className="text-sm whitespace-pre-wrap">{agent.prompt}</pre>
                </ScrollArea>
              </div>

              {agent.systemPrompt && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">System Prompt</h3>
                  <ScrollArea className="h-24 w-full border rounded-md p-3">
                    <pre className="text-sm whitespace-pre-wrap">{agent.systemPrompt}</pre>
                  </ScrollArea>
                </div>
              )}
            </TabsContent>

            <TabsContent value="configuration" className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      Model Configuration
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Model:</span>
                      <span className="text-sm font-mono">{agent.model}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Temperature:</span>
                      <span className="text-sm font-mono">{agent.temperature}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Max Tokens:</span>
                      <span className="text-sm font-mono">{agent.maxTokens}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Bot className="h-4 w-4" />
                      Agent Settings
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Status:</span>
                      <Badge className={getStatusColor(agent.status)}>
                        {agent.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Visibility:</span>
                      <span className="text-sm">
                        {agent.isPublic ? "Public" : "Private"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Version:</span>
                      <span className="text-sm font-mono">v{agent.version}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {(agent as any).template && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Template Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Template:</span>
                        <span className="text-sm font-medium">
                          {(agent as any).template.name}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Type:</span>
                        <span className="text-sm">
                          {(agent as any).template.type}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="executions" className="mt-4">
              <AgentExecutions agentId={agentId} />
            </TabsContent>

            <TabsContent value="analytics" className="mt-4">
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Analytics dashboard coming soon</p>
                <p className="text-sm text-gray-400 mt-1">
                  Detailed performance metrics and insights will be available here.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default AgentDetails;

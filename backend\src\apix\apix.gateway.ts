import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from "@nestjs/websockets";
import { Server, Socket } from "socket.io";
import { Injectable, Logger, UseGuards } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { PrismaService } from "../prisma/prisma.service";
import { ApixService } from "./apix.service";
import { BillingService } from "../billing/billing.service";
import { AnalyticsService } from "../analytics/analytics.service";

interface AuthenticatedSocket extends Socket {
  userId?: string;
  organizationId?: string;
  role?: string;
}

@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    credentials: true,
  },
  namespace: "/apix",
})
@Injectable()
export class ApixGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ApixGateway.name);
  private connectedClients = new Map<string, AuthenticatedSocket>();
  private organizationRooms = new Map<string, Set<string>>();

  constructor(
    private jwtService: JwtService,
    private prisma: PrismaService,
    private apixService: ApixService,
    private billingService: BillingService,
    private analyticsService: AnalyticsService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token =
        client.handshake.auth.token ||
        client.handshake.headers.authorization?.replace("Bearer ", "");

      if (!token) {
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        include: { organization: true },
      });

      if (!user || !user.isActive || user.organization.status !== "ACTIVE") {
        client.disconnect();
        return;
      }

      client.userId = user.id;
      client.organizationId = user.organizationId;
      client.role = user.role;

      // Add to organization room
      const orgRoom = `org:${user.organizationId}`;
      client.join(orgRoom);

      // Track connection
      this.connectedClients.set(client.id, client);

      if (!this.organizationRooms.has(user.organizationId)) {
        this.organizationRooms.set(user.organizationId, new Set());
      }
      this.organizationRooms.get(user.organizationId)!.add(client.id);

      // Track analytics
      await this.analyticsService.track("apix_connection", {
        userId: user.id,
        organizationId: user.organizationId,
        socketId: client.id,
      });

      this.logger.log(`Client connected: ${user.email} (${client.id})`);

      // Send connection confirmation
      client.emit("connected", {
        userId: user.id,
        organizationId: user.organizationId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error("Connection authentication failed:", error);
      client.disconnect();
    }
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    if (client.organizationId) {
      const orgClients = this.organizationRooms.get(client.organizationId);
      if (orgClients) {
        orgClients.delete(client.id);
        if (orgClients.size === 0) {
          this.organizationRooms.delete(client.organizationId);
        }
      }

      // Track analytics
      await this.analyticsService.track("apix_disconnection", {
        userId: client.userId,
        organizationId: client.organizationId,
        socketId: client.id,
      });
    }

    this.connectedClients.delete(client.id);
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage("subscribe")
  async handleSubscribe(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { channels: string[] },
  ) {
    if (!client.organizationId) return;

    for (const channel of data.channels) {
      // Validate channel access
      if (this.isChannelAllowed(channel, client.organizationId, client.role)) {
        client.join(channel);
        this.logger.debug(`Client ${client.id} subscribed to ${channel}`);
      }
    }

    client.emit("subscribed", { channels: data.channels });
  }

  @SubscribeMessage("unsubscribe")
  async handleUnsubscribe(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { channels: string[] },
  ) {
    for (const channel of data.channels) {
      client.leave(channel);
      this.logger.debug(`Client ${client.id} unsubscribed from ${channel}`);
    }

    client.emit("unsubscribed", { channels: data.channels });
  }

  // Public methods for broadcasting events
  async broadcastToOrganization(
    organizationId: string,
    event: string,
    data: any,
  ) {
    const room = `org:${organizationId}`;
    this.server.to(room).emit(event, {
      ...data,
      timestamp: new Date().toISOString(),
    });

    // Track event
    await this.analyticsService.track("apix_broadcast", {
      organizationId,
      event,
      recipientCount: this.organizationRooms.get(organizationId)?.size || 0,
    });
  }

  async broadcastToChannel(channel: string, event: string, data: any) {
    this.server.to(channel).emit(event, {
      ...data,
      timestamp: new Date().toISOString(),
    });
  }

  async broadcastToUser(userId: string, event: string, data: any) {
    const userSockets = Array.from(this.connectedClients.values()).filter(
      (socket) => socket.userId === userId,
    );

    for (const socket of userSockets) {
      socket.emit(event, {
        ...data,
        timestamp: new Date().toISOString(),
      });
    }
  }

  getConnectedClientsCount(organizationId?: string): number {
    if (organizationId) {
      return this.organizationRooms.get(organizationId)?.size || 0;
    }
    return this.connectedClients.size;
  }

  private isChannelAllowed(
    channel: string,
    organizationId: string,
    role: string,
  ): boolean {
    // Organization-scoped channels
    if (channel.startsWith(`org:${organizationId}`)) {
      return true;
    }

    // Admin-only channels
    if (
      channel.includes("admin") &&
      !["ORG_ADMIN", "SUPER_ADMIN"].includes(role)
    ) {
      return false;
    }

    // Public channels
    const publicChannels = ["system", "announcements"];
    return publicChannels.some((pub) => channel.includes(pub));
  }
}

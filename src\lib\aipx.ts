// @synapseai/aipx SDK Interface Layer
// This is a portable SDK that unifies UAUI logic into reusable modules

import { io, Socket } from "socket.io-client";
import { create } from "zustand";

// Types
export interface AIPXRequest {
  userId: string;
  sessionId: string;
  message: string;
  appType: "widget" | "dashboard" | "crm";
  metadata?: Record<string, any>;
  context?: Record<string, any>;
  agentId?: string;
  toolId?: string;
  hybridId?: string;
}

export interface AIPXResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, any>;
  };
  error?: string;
  state_update?: Record<string, any>;
  hitl_request?: {
    type: string;
    data: Record<string, any>;
  };
}

export interface AIPXApp {
  appId: string;
  appType: string;
  subscribe: (channels: string[]) => Promise<void>;
  unsubscribe: (channels: string[]) => Promise<void>;
  processAIRequest: (request: Omit<AIPXRequest, "userId" | "appType">) => Promise<AIPXResponse>;
  onEvent: (eventType: string, callback: (payload: any) => void) => () => void;
  onAnyEvent: (callback: (eventType: string, payload: any) => void) => () => void;
  syncState: (toAppId: string, state: Record<string, any>) => Promise<void>;
  getSessionState: (sessionId: string) => Promise<Record<string, any>>;
  updateSessionState: (sessionId: string, updates: Record<string, any>) => Promise<void>;
  disconnect: () => void;
}

export interface AIPXOptions {
  apiUrl?: string;
  wsUrl?: string;
  authToken?: string;
  autoReconnect?: boolean;
  reconnectDelay?: number;
  debug?: boolean;
}

// Store types
interface AIPXStore {
  socket: Socket | null;
  connected: boolean;
  connecting: boolean;
  userId: string | null;
  organizationId: string | null;
  registeredApps: Map<string, AIPXApp>;
  eventListeners: Map<string, Set<(payload: any) => void>>;
  anyEventListeners: Set<(eventType: string, payload: any) => void>;
  connectionListeners: Set<(connected: boolean) => void>;
  pendingMessages: Map<string, {
    resolve: (value: AIPXResponse) => void;
    reject: (reason: any) => void;
    timeout: NodeJS.Timeout;
  }>;
  
  // Actions
  setSocket: (socket: Socket | null) => void;
  setConnected: (connected: boolean) => void;
  setConnecting: (connecting: boolean) => void;
  setUserId: (userId: string | null) => void;
  setOrganizationId: (organizationId: string | null) => void;
  registerApp: (appId: string, app: AIPXApp) => void;
  unregisterApp: (appId: string) => void;
  addEventListener: (eventType: string, callback: (payload: any) => void) => void;
  removeEventListener: (eventType: string, callback: (payload: any) => void) => void;
  addAnyEventListener: (callback: (eventType: string, payload: any) => void) => void;
  removeAnyEventListener: (callback: (eventType: string, payload: any) => void) => void;
  addConnectionListener: (callback: (connected: boolean) => void) => void;
  removeConnectionListener: (callback: (connected: boolean) => void) => void;
  addPendingMessage: (messageId: string, handlers: {
    resolve: (value: AIPXResponse) => void;
    reject: (reason: any) => void;
    timeout: NodeJS.Timeout;
  }) => void;
  resolvePendingMessage: (messageId: string, response: AIPXResponse) => void;
  rejectPendingMessage: (messageId: string, error: any) => void;
  clearPendingMessage: (messageId: string) => void;
}

// Create store
const useAIPXStore = create<AIPXStore>((set, get) => ({
  socket: null,
  connected: false,
  connecting: false,
  userId: null,
  organizationId: null,
  registeredApps: new Map(),
  eventListeners: new Map(),
  anyEventListeners: new Set(),
  connectionListeners: new Set(),
  pendingMessages: new Map(),
  
  setSocket: (socket) => set({ socket }),
  setConnected: (connected) => {
    set({ connected });
    get().connectionListeners.forEach(listener => listener(connected));
  },
  setConnecting: (connecting) => set({ connecting }),
  setUserId: (userId) => set({ userId }),
  setOrganizationId: (organizationId) => set({ organizationId }),
  
  registerApp: (appId, app) => {
    const apps = get().registeredApps;
    apps.set(appId, app);
    set({ registeredApps: apps });
  },
  
  unregisterApp: (appId) => {
    const apps = get().registeredApps;
    apps.delete(appId);
    set({ registeredApps: apps });
  },
  
  addEventListener: (eventType, callback) => {
    const listeners = get().eventListeners;
    if (!listeners.has(eventType)) {
      listeners.set(eventType, new Set());
    }
    listeners.get(eventType)!.add(callback);
    set({ eventListeners: listeners });
  },
  
  removeEventListener: (eventType, callback) => {
    const listeners = get().eventListeners;
    if (listeners.has(eventType)) {
      listeners.get(eventType)!.delete(callback);
      if (listeners.get(eventType)!.size === 0) {
        listeners.delete(eventType);
      }
    }
    set({ eventListeners: listeners });
  },
  
  addAnyEventListener: (callback) => {
    const listeners = get().anyEventListeners;
    listeners.add(callback);
    set({ anyEventListeners: listeners });
  },
  
  removeAnyEventListener: (callback) => {
    const listeners = get().anyEventListeners;
    listeners.delete(callback);
    set({ anyEventListeners: listeners });
  },
  
  addConnectionListener: (callback) => {
    const listeners = get().connectionListeners;
    listeners.add(callback);
    set({ connectionListeners: listeners });
  },
  
  removeConnectionListener: (callback) => {
    const listeners = get().connectionListeners;
    listeners.delete(callback);
    set({ connectionListeners: listeners });
  },
  
  addPendingMessage: (messageId, handlers) => {
    const pendingMessages = get().pendingMessages;
    pendingMessages.set(messageId, handlers);
    set({ pendingMessages });
  },
  
  resolvePendingMessage: (messageId, response) => {
    const pendingMessages = get().pendingMessages;
    const pending = pendingMessages.get(messageId);
    if (pending) {
      clearTimeout(pending.timeout);
      pending.resolve(response);
      pendingMessages.delete(messageId);
      set({ pendingMessages });
    }
  },
  
  rejectPendingMessage: (messageId, error) => {
    const pendingMessages = get().pendingMessages;
    const pending = pendingMessages.get(messageId);
    if (pending) {
      clearTimeout(pending.timeout);
      pending.reject(error);
      pendingMessages.delete(messageId);
      set({ pendingMessages });
    }
  },
  
  clearPendingMessage: (messageId) => {
    const pendingMessages = get().pendingMessages;
    const pending = pendingMessages.get(messageId);
    if (pending) {
      clearTimeout(pending.timeout);
      pendingMessages.delete(messageId);
      set({ pendingMessages });
    }
  },
}));

// Main AIPX Core class
export class AIPXCore {
  private options: AIPXOptions;
  private socket: Socket | null = null;
  private apiUrl: string;
  private wsUrl: string;
  private authToken: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private debug: boolean;
  
  constructor(options: AIPXOptions = {}) {
    this.options = {
      apiUrl: options.apiUrl || "http://localhost:3001/api/v1",
      wsUrl: options.wsUrl || "http://localhost:3001",
      authToken: options.authToken || null,
      autoReconnect: options.autoReconnect !== false,
      reconnectDelay: options.reconnectDelay || 3000,
      debug: options.debug || false,
    };
    
    this.apiUrl = this.options.apiUrl;
    this.wsUrl = this.options.wsUrl;
    this.authToken = this.options.authToken;
    this.debug = this.options.debug;
    
    // Initialize store
    useAIPXStore.getState().setConnecting(false);
    useAIPXStore.getState().setConnected(false);
  }
  
  // Initialize connection
  async connect(token?: string): Promise<void> {
    if (token) {
      this.authToken = token;
    }
    
    if (!this.authToken) {
      throw new Error("Authentication token is required to connect");
    }
    
    if (useAIPXStore.getState().connecting) {
      this.log("Already connecting, skipping connect call");
      return;
    }
    
    if (useAIPXStore.getState().connected) {
      this.log("Already connected, skipping connect call");
      return;
    }
    
    useAIPXStore.getState().setConnecting(true);
    
    try {
      this.log("Connecting to APIX WebSocket server...");
      
      this.socket = io(`${this.wsUrl}/apix`, {
        auth: {
          token: this.authToken,
        },
        reconnection: this.options.autoReconnect,
        reconnectionDelay: this.options.reconnectDelay,
        reconnectionAttempts: this.maxReconnectAttempts,
      });
      
      useAIPXStore.getState().setSocket(this.socket);
      
      // Setup event listeners
      this.setupSocketListeners();
      
      // Return a promise that resolves when connected
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error("Connection timeout"));
          useAIPXStore.getState().setConnecting(false);
        }, 10000);
        
        this.socket!.once("connected", (data) => {
          clearTimeout(timeout);
          useAIPXStore.getState().setConnecting(false);
          useAIPXStore.getState().setConnected(true);
          useAIPXStore.getState().setUserId(data.userId);
          useAIPXStore.getState().setOrganizationId(data.organizationId);
          this.log("Connected to APIX server", data);
          resolve();
        });
        
        this.socket!.once("connect_error", (error) => {
          clearTimeout(timeout);
          useAIPXStore.getState().setConnecting(false);
          this.log("Connection error", error);
          reject(error);
        });
      });
    } catch (error) {
      useAIPXStore.getState().setConnecting(false);
      this.log("Connection error", error);
      throw error;
    }
  }
  
  // Disconnect from server
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      useAIPXStore.getState().setSocket(null);
      useAIPXStore.getState().setConnected(false);
      this.log("Disconnected from APIX server");
    }
  }
  
  // Register a new app
  registerApp(appId: string, appType: string): AIPXApp {
    this.log(`Registering app: ${appId} (${appType})`);
    
    const app: AIPXApp = {
      appId,
      appType,
      
      subscribe: async (channels: string[]) => {
        if (!useAIPXStore.getState().connected) {
          throw new Error("Not connected to APIX server");
        }
        
        return new Promise<void>((resolve, reject) => {
          useAIPXStore.getState().socket!.emit("subscribe", { channels }, (response: any) => {
            if (response.error) {
              reject(new Error(response.error));
            } else {
              resolve();
            }
          });
        });
      },
      
      unsubscribe: async (channels: string[]) => {
        if (!useAIPXStore.getState().connected) {
          throw new Error("Not connected to APIX server");
        }
        
        return new Promise<void>((resolve, reject) => {
          useAIPXStore.getState().socket!.emit("unsubscribe", { channels }, (response: any) => {
            if (response.error) {
              reject(new Error(response.error));
            } else {
              resolve();
            }
          });
        });
      },
      
      processAIRequest: async (request) => {
        const userId = useAIPXStore.getState().userId;
        if (!userId) {
          throw new Error("User ID not available, not connected to APIX server");
        }
        
        const fullRequest: AIPXRequest = {
          ...request,
          userId,
          appType,
        };
        
        return this.processAIRequest(fullRequest);
      },
      
      onEvent: (eventType, callback) => {
        useAIPXStore.getState().addEventListener(eventType, callback);
        return () => useAIPXStore.getState().removeEventListener(eventType, callback);
      },
      
      onAnyEvent: (callback) => {
        const wrappedCallback = (eventType: string, payload: any) => callback(eventType, payload);
        useAIPXStore.getState().addAnyEventListener(wrappedCallback);
        return () => useAIPXStore.getState().removeAnyEventListener(wrappedCallback);
      },
      
      syncState: async (toAppId, state) => {
        return this.syncState(appId, toAppId, state);
      },
      
      getSessionState: async (sessionId) => {
        return this.getSessionState(sessionId);
      },
      
      updateSessionState: async (sessionId, updates) => {
        return this.updateSessionState(sessionId, updates);
      },
      
      disconnect: () => {
        useAIPXStore.getState().unregisterApp(appId);
      },
    };
    
    useAIPXStore.getState().registerApp(appId, app);
    return app;
  }
  
  // Process an AI request
  async processAIRequest(request: AIPXRequest): Promise<AIPXResponse> {
    if (!useAIPXStore.getState().connected) {
      throw new Error("Not connected to APIX server");
    }
    
    this.log("Processing AI request", request);
    
    try {
      const response = await fetch(`${this.apiUrl}/uaui/process`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.authToken}`,
        },
        body: JSON.stringify(request),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to process AI request");
      }
      
      const result = await response.json();
      return result;
    } catch (error) {
      this.log("Error processing AI request", error);
      throw error;
    }
  }
  
  // Sync state between apps
  async syncState(
    fromAppId: string,
    toAppId: string,
    state: Record<string, any>
  ): Promise<void> {
    if (!useAIPXStore.getState().connected) {
      throw new Error("Not connected to APIX server");
    }
    
    this.log(`Syncing state from ${fromAppId} to ${toAppId}`, state);
    
    try {
      const response = await fetch(`${this.apiUrl}/uaui/sync-state`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.authToken}`,
        },
        body: JSON.stringify({
          fromAppId,
          toAppId,
          state,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to sync state");
      }
    } catch (error) {
      this.log("Error syncing state", error);
      throw error;
    }
  }
  
  // Get session state
  async getSessionState(sessionId: string): Promise<Record<string, any>> {
    if (!this.authToken) {
      throw new Error("Authentication token is required");
    }
    
    try {
      const response = await fetch(`${this.apiUrl}/uaui/session/${sessionId}/state`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${this.authToken}`,
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to get session state");
      }
      
      const result = await response.json();
      return result.data || {};
    } catch (error) {
      this.log("Error getting session state", error);
      throw error;
    }
  }
  
  // Update session state
  async updateSessionState(
    sessionId: string,
    updates: Record<string, any>
  ): Promise<void> {
    if (!this.authToken) {
      throw new Error("Authentication token is required");
    }
    
    try {
      const response = await fetch(`${this.apiUrl}/uaui/session/${sessionId}/state`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.authToken}`,
        },
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update session state");
      }
    } catch (error) {
      this.log("Error updating session state", error);
      throw error;
    }
  }
  
  // Register event listener
  onEvent(eventType: string, callback: (payload: any) => void): () => void {
    useAIPXStore.getState().addEventListener(eventType, callback);
    return () => useAIPXStore.getState().removeEventListener(eventType, callback);
  }
  
  // Register connection state listener
  onConnectionChange(callback: (connected: boolean) => void): () => void {
    useAIPXStore.getState().addConnectionListener(callback);
    callback(useAIPXStore.getState().connected);
    return () => useAIPXStore.getState().removeConnectionListener(callback);
  }
  
  // Setup socket event listeners
  private setupSocketListeners(): void {
    if (!this.socket) return;
    
    this.socket.on("connect", () => {
      this.log("Socket connected");
    });
    
    this.socket.on("disconnect", (reason) => {
      this.log(`Socket disconnected: ${reason}`);
      useAIPXStore.getState().setConnected(false);
      
      if (
        this.options.autoReconnect &&
        reason !== "io client disconnect" &&
        this.reconnectAttempts < this.maxReconnectAttempts
      ) {
        this.reconnectAttempts++;
        this.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        setTimeout(() => {
          this.connect(this.authToken!).catch((error) => {
            this.log("Reconnection failed", error);
          });
        }, this.options.reconnectDelay);
      }
    });
    
    this.socket.on("error", (error) => {
      this.log("Socket error", error);
    });
    
    // Handle all incoming events
    this.socket.onAny((eventType, payload) => {
      this.log(`Received event: ${eventType}`, payload);
      
      // Notify specific event listeners
      const listeners = useAIPXStore.getState().eventListeners.get(eventType);
      if (listeners) {
        listeners.forEach((listener) => {
          try {
            listener(payload);
          } catch (error) {
            this.log(`Error in event listener for ${eventType}`, error);
          }
        });
      }
      
      // Notify "any" event listeners
      useAIPXStore.getState().anyEventListeners.forEach((listener) => {
        try {
          listener(eventType, payload);
        } catch (error) {
          this.log(`Error in any-event listener for ${eventType}`, error);
        }
      });
    });
  }
  
  // Logging helper
  private log(message: string, ...args: any[]): void {
    if (this.debug) {
      console.log(`[AIPX] ${message}`, ...args);
    }
  }
}

// Create and export a singleton instance
export const aipx = new AIPXCore();
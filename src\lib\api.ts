import {
  Agent,
  AgentStatus,
  Tool,
  ToolStatus,
  ToolExecution,
  Provider,
  ProviderStatus,
  ProviderType,
  ApiResponse,
  PaginatedResponse,
} from "../../shared/types";
import { apiClient } from "./aipx";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api/v1";

class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any,
  ) {
    super(message);
    this.name = "ApiError";
  }
}

class ApiClient {
  private baseUrl: string;
  private token: string | null = null;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
    this.token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== "undefined") {
      localStorage.setItem("auth_token", token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token");
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: HeadersInit = {
      "Content-Type": "application/json",
      ...options.headers,
    };

    if (this.token) {
      (headers as Record<string, string>)["Authorization"] =
        `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.message ||
            `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData,
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(
        error instanceof Error ? error.message : "Network error",
        0,
      );
    }
  }

  // Agent API methods
  async getAgents(params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: AgentStatus;
    isPublic?: boolean;
    tags?: string[];
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }): Promise<PaginatedResponse<Agent>> {
    try {
      const searchParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach((v) => searchParams.append(key, v));
            } else {
              searchParams.append(key, String(value));
            }
          }
        });
      }

      return await this.request<PaginatedResponse<Agent>>(
        `/agents?${searchParams.toString()}`,
      );
    } catch (error) {
      console.warn("Backend not available, using mock data for agents");
      return mockApiClient.getAgents(params);
    }
  }

  async getAgent(id: string): Promise<ApiResponse<Agent>> {
    return this.request<ApiResponse<Agent>>(`/agents/${id}`);
  }

  async createAgent(data: {
    name: string;
    description?: string;
    prompt: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
    status?: AgentStatus;
    isPublic?: boolean;
    tags?: string[];
    metadata?: Record<string, any>;
    templateId?: string;
  }): Promise<ApiResponse<Agent>> {
    return this.request<ApiResponse<Agent>>("/agents", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateAgent(
    id: string,
    data: Partial<{
      name: string;
      description?: string;
      prompt: string;
      model: string;
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      status?: AgentStatus;
      isPublic?: boolean;
      tags?: string[];
      metadata?: Record<string, any>;
      templateId?: string;
      version?: number;
    }>,
  ): Promise<ApiResponse<Agent>> {
    return this.request<ApiResponse<Agent>>(`/agents/${id}`, {
      method: "PATCH",
      body: JSON.stringify(data),
    });
  }

  async deleteAgent(id: string): Promise<void> {
    await this.request(`/agents/${id}`, {
      method: "DELETE",
    });
  }

  async executeAgent(
    id: string,
    data: {
      input: string;
      sessionId?: string;
      context?: Record<string, any>;
      temperature?: number;
      maxTokens?: number;
      metadata?: Record<string, any>;
    },
  ): Promise<
    ApiResponse<{ executionId: string; status: string; message: string }>
  > {
    return this.request<
      ApiResponse<{ executionId: string; status: string; message: string }>
    >(`/agents/${id}/execute`, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async getAgentExecutions(
    id: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<
    PaginatedResponse<{
      id: string;
      status: string;
      input: any;
      output?: any;
      error?: string;
      duration?: number;
      tokens?: number;
      cost?: number;
      createdAt: string;
      completedAt?: string;
    }>
  > {
    return this.request<
      PaginatedResponse<{
        id: string;
        status: string;
        input: any;
        output?: any;
        error?: string;
        duration?: number;
        tokens?: number;
        cost?: number;
        createdAt: string;
        completedAt?: string;
      }>
    >(`/agents/${id}/executions?page=${page}&limit=${limit}`);
  }

  async duplicateAgent(id: string): Promise<ApiResponse<Agent>> {
    return this.request<ApiResponse<Agent>>(`/agents/${id}/duplicate`, {
      method: "POST",
    });
  }

  async getAgentStats(): Promise<
    ApiResponse<{
      byStatus: Record<string, number>;
      totalExecutions: number;
      recentExecutions: number;
    }>
  > {
    try {
      return await this.request<
        ApiResponse<{
          byStatus: Record<string, number>;
          totalExecutions: number;
          recentExecutions: number;
        }>
      >("/agents/stats");
    } catch (error) {
      // Fallback to mock data if backend is not available
      console.warn("Backend not available, using mock data for agent stats");
      return mockApiClient.getAgentStats();
    }
  }

  // Tool API methods
  async getTools(params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: ToolStatus;
    isPublic?: boolean;
    tags?: string[];
    category?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }): Promise<PaginatedResponse<Tool>> {
    try {
      const searchParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              value.forEach((v) => searchParams.append(key, v));
            } else {
              searchParams.append(key, String(value));
            }
          }
        });
      }

      return await this.request<PaginatedResponse<Tool>>(
        `/tools?${searchParams.toString()}`,
      );
    } catch (error) {
      console.warn("Backend not available, using mock data for tools");
      return mockApiClient.getTools(params);
    }
  }

  async getTool(id: string): Promise<ApiResponse<Tool>> {
    return this.request<ApiResponse<Tool>>(`/tools/${id}`);
  }

  async createTool(data: {
    name: string;
    description?: string;
    schema: Record<string, any>;
    endpoint?: string;
    method?: string;
    headers?: Record<string, any>;
    authentication?: Record<string, any>;
    status?: ToolStatus;
    isPublic?: boolean;
    tags?: string[];
    category?: string;
    metadata?: Record<string, any>;
  }): Promise<ApiResponse<Tool>> {
    return this.request<ApiResponse<Tool>>("/tools", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateTool(
    id: string,
    data: Partial<{
      name: string;
      description?: string;
      schema: Record<string, any>;
      endpoint?: string;
      method?: string;
      headers?: Record<string, any>;
      authentication?: Record<string, any>;
      status?: ToolStatus;
      isPublic?: boolean;
      tags?: string[];
      category?: string;
      metadata?: Record<string, any>;
      version?: number;
    }>,
  ): Promise<ApiResponse<Tool>> {
    return this.request<ApiResponse<Tool>>(`/tools/${id}`, {
      method: "PATCH",
      body: JSON.stringify(data),
    });
  }

  async deleteTool(id: string): Promise<void> {
    await this.request(`/tools/${id}`, {
      method: "DELETE",
    });
  }

  async executeTool(
    id: string,
    data: {
      input: Record<string, any>;
      sessionId?: string;
      context?: Record<string, any>;
      metadata?: Record<string, any>;
    },
  ): Promise<
    ApiResponse<{
      executionId: string;
      status: string;
      result: any;
      error?: string;
      duration: number;
      cost: number;
    }>
  > {
    return this.request<
      ApiResponse<{
        executionId: string;
        status: string;
        result: any;
        error?: string;
        duration: number;
        cost: number;
      }>
    >(`/tools/${id}/execute`, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async getToolExecutions(
    id: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResponse<ToolExecution>> {
    return this.request<PaginatedResponse<ToolExecution>>(
      `/tools/${id}/executions?page=${page}&limit=${limit}`,
    );
  }

  async duplicateTool(id: string): Promise<ApiResponse<Tool>> {
    return this.request<ApiResponse<Tool>>(`/tools/${id}/duplicate`, {
      method: "POST",
    });
  }

  async getToolStats(): Promise<
    ApiResponse<{
      byStatus: Record<string, number>;
      totalExecutions: number;
      recentExecutions: number;
    }>
  > {
    try {
      return await this.request<
        ApiResponse<{
          byStatus: Record<string, number>;
          totalExecutions: number;
          recentExecutions: number;
        }>
      >("/tools/stats");
    } catch (error) {
      console.warn("Backend not available, using mock data for tool stats");
      return mockApiClient.getToolStats();
    }
  }

   // Provider API methods
  async getProviders(params?: {
    page?: number;
    limit?: number;
    search?: string;
    type?: ProviderType;
    status?: ProviderStatus;
    isActive?: boolean;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }): Promise<PaginatedResponse<Provider>> {
    try {
      const searchParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, String(value));
          }
        });
      }

      return await this.request<PaginatedResponse<Provider>>(
        `/providers?${searchParams.toString()}`,
      );
    } catch (error) {
      console.warn("Backend not available, using mock data for providers");
      return mockApiClient.getProviders(params);
    }
  }

  async getProvider(id: string): Promise<ApiResponse<Provider>> {
    return this.request<ApiResponse<Provider>>(`/providers/${id}`);
  }

  async createProvider(data: {
    name: string;
    description?: string;
    type: ProviderType;
    configuration: Record<string, any>;
    models: string[];
    capabilities: string[];
    status?: ProviderStatus;
    isActive?: boolean;
    reliability?: number;
    costPerToken?: number;
    averageLatency?: number;
    priority?: number;
    metadata?: Record<string, any>;
  }): Promise<ApiResponse<Provider>> {
    return this.request<ApiResponse<Provider>>("/providers", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateProvider(
    id: string,
    data: Partial<{
      name: string;
      description?: string;
      type: ProviderType;
      configuration: Record<string, any>;
      models: string[];
      capabilities: string[];
      status?: ProviderStatus;
      isActive?: boolean;
      reliability?: number;
      costPerToken?: number;
      averageLatency?: number;
      priority?: number;
      metadata?: Record<string, any>;
    }>,
  ): Promise<ApiResponse<Provider>> {
    return this.request<ApiResponse<Provider>>(`/providers/${id}`, {
      method: "PATCH",
      body: JSON.stringify(data),
    });
  }

  async deleteProvider(id: string): Promise<void> {
    await this.request(`/providers/${id}`, {
      method: "DELETE",
    });
  }

  async testProvider(
    id: string,
  ): Promise<
    ApiResponse<{
      status: string;
      result: any;
      error?: string;
      duration: number;
      timestamp: string;
    }>
  > {
    return this.request<
      ApiResponse<{
        status: string;
        result: any;
        error?: string;
        duration: number;
        timestamp: string;
      }>
    >(`/providers/${id}/test`, {
      method: "POST",
    });
  }

  async selectOptimalProvider(data: {
    model: string;
    task: string;
    context?: any;
  }): Promise<ApiResponse<Provider>> {
    return this.request<ApiResponse<Provider>>("/providers/select", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async getAvailableProviders(): Promise<
    ApiResponse<
      {
        type: string;
        name: string;
        models: string[];
        capabilities: string[];
      }[]
    >
  > {
    return this.request<
      ApiResponse<
        {
          type: string;
          name: string;
          models: string[];
          capabilities: string[];
        }[]
      >
    >("/providers/available");
  }

  async getProviderStats(): Promise<
    ApiResponse<{
      byType: Record<string, number>;
      byStatus: Record<string, number>;
      totalExecutions: number;
      recentlyUsed: number;
    }>
  > {
    try {
      return await this.request<
        ApiResponse<{
          byType: Record<string, number>;
          byStatus: Record<string, number>;
          totalExecutions: number;
          recentlyUsed: number;
        }>
      >("/providers/stats");
    } catch (error) {
      console.warn("Backend not available, using mock data for provider stats");
      return mockApiClient.getProviderStats();
    }
  }
}



export const apiClient = new ApiClient();
export { ApiError };
export type { ApiResponse, PaginatedResponse };

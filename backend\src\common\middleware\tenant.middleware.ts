import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
  ForbiddenException,
} from "@nestjs/common";
import { Request, Response, NextFunction } from "express";
import { JwtService } from "@nestjs/jwt";
import { PrismaService } from "../../prisma/prisma.service";
import { Logger } from "@nestjs/common";

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    organizationId: string;
    role: string;
  };
  organization?: {
    id: string;
    name: string;
    slug: string;
    status: string;
  };
}

@Injectable()
export class TenantMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantMiddleware.name);

  constructor(
    private jwtService: JwtService,
    private prisma: PrismaService,
  ) {}

  async use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      // Skip tenant validation for public routes
      if (this.isPublicRoute(req.path)) {
        return next();
      }

      // Extract JWT token from Authorization header
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        throw new UnauthorizedException(
          "Missing or invalid authorization header",
        );
      }

      const token = authHeader.substring(7);

      // Verify and decode JWT token
      let payload: any;
      try {
        payload = this.jwtService.verify(token);
      } catch (error) {
        throw new UnauthorizedException("Invalid or expired token");
      }

      // Fetch user with organization details
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        include: {
          organization: true,
        },
      });

      if (!user) {
        throw new UnauthorizedException("User not found");
      }

      if (!user.isActive) {
        throw new ForbiddenException("User account is inactive");
      }

      if (user.organization.status !== "ACTIVE") {
        throw new ForbiddenException("Organization is not active");
      }

      // Attach user and organization to request
      req.user = {
        id: user.id,
        email: user.email,
        organizationId: user.organizationId,
        role: user.role,
      };

      req.organization = {
        id: user.organization.id,
        name: user.organization.name,
        slug: user.organization.slug,
        status: user.organization.status,
      };

      // Update last login timestamp
      await this.prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      this.logger.debug(
        `Tenant context set for user ${user.email} in organization ${user.organization.name}`,
      );

      next();
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      this.logger.error("Tenant middleware error:", error);
      throw new UnauthorizedException("Authentication failed");
    }
  }

  private isPublicRoute(path: string): boolean {
    const publicRoutes = [
      "/health",
      "/auth/login",
      "/auth/register",
      "/auth/refresh",
      "/auth/forgot-password",
      "/auth/reset-password",
      "/widgets/embed", // Public widget endpoints
    ];

    return publicRoutes.some((route) => path.startsWith(route));
  }
}

/**
 * Decorator to get current user from request
 */
export const CurrentUser = () => {
  return (target: any, propertyKey: string, parameterIndex: number) => {
    return {
      ...target,
      [propertyKey]: {
        ...target[propertyKey],
        parameters: {
          ...target[propertyKey]?.parameters,
          [parameterIndex]: {
            type: "user",
          },
        },
      },
    };
  };
};

/**
 * Decorator to get current organization from request
 */
export const CurrentOrganization = () => {
  return (target: any, propertyKey: string, parameterIndex: number) => {
    return {
      ...target,
      [propertyKey]: {
        ...target[propertyKey],
        parameters: {
          ...target[propertyKey]?.parameters,
          [parameterIndex]: {
            type: "organization",
          },
        },
      },
    };
  };
};

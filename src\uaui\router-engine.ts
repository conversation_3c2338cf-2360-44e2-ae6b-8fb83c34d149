import { Injectable, Logger } from "@nestjs/common";
import { ApixService } from "../apix/apix.service";
import { EventBus } from "./event-bus";

export interface RouterCommand {
  type: string;
  payload: Record<string, any>;
  target?: {
    appId?: string;
    userId?: string;
    sessionId?: string;
    organizationId: string;
  };
}

@Injectable()
export class RouterEngine {
  private readonly logger = new Logger(RouterEngine.name);

  constructor(
    private apixService: ApixService,
    private eventBus: EventBus
  ) {}

  async route(command: RouterCommand): Promise<void> {
    try {
      this.logger.log(`Routing command: ${command.type}`);

      const { type, payload, target } = command;

      if (!target || !target.organizationId) {
        throw new Error("Target organization is required for routing commands");
      }

      // Emit as a special control_signal event
      await this.apixService.emit({
        type: "control_signal",
        payload: {
          command: type,
          data: payload,
          timestamp: new Date().toISOString(),
        },
        organizationId: target.organizationId,
        userId: target.userId,
        sessionId: target.sessionId,
        metadata: {
          targetAppId: target.appId,
          routerCommand: true,
        },
      });

      // Also emit through the event bus for internal subscribers
      await this.eventBus.emit({
        type: `router:${type}`,
        payload,
        organizationId: target.organizationId,
        userId: target.userId,
        sessionId: target.sessionId,
        metadata: {
          targetAppId: target.appId,
          routerCommand: true,
        },
      });
    } catch (error) {
      this.logger.error(`Error routing command: ${error.message}`, error.stack);
      throw error;
    }
  }

  async injectDomInstruction(
    organizationId: string,
    sessionId: string,
    instruction: {
      selector: string;
      action: "click" | "input" | "select" | "focus" | "scroll" | "highlight";
      value?: string;
      metadata?: Record<string, any>;
    }
  ): Promise<void> {
    await this.route({
      type: "inject_dom_instruction",
      payload: instruction,
      target: {
        organizationId,
        sessionId,
      },
    });
  }

  async routeToDashboard(
    organizationId: string,
    userId: string,
    destination: {
      path: string;
      params?: Record<string, string>;
      replace?: boolean;
    }
  ): Promise<void> {
    await this.route({
      type: "route_to_dashboard",
      payload: destination,
      target: {
        organizationId,
        userId,
      },
    });
  }

  async showNotification(
    organizationId: string,
    userId: string,
    notification: {
      title: string;
      message: string;
      type?: "info" | "success" | "warning" | "error";
      duration?: number;
      actions?: Array<{
        label: string;
        action: string;
        data?: Record<string, any>;
      }>;
    }
  ): Promise<void> {
    await this.route({
      type: "show_notification",
      payload: notification,
      target: {
        organizationId,
        userId,
      },
    });
  }

  async updateUIState(
    organizationId: string,
    appId: string,
    updates: Record<string, any>
  ): Promise<void> {
    await this.route({
      type: "update_ui_state",
      payload: updates,
      target: {
        organizationId,
        appId,
      },
    });
  }

  async triggerRefresh(
    organizationId: string,
    appId: string,
    target?: string
  ): Promise<void> {
    await this.route({
      type: "trigger_refresh",
      payload: {
        target,
        timestamp: new Date().toISOString(),
      },
      target: {
        organizationId,
        appId,
      },
    });
  }

  async openModal(
    organizationId: string,
    userId: string,
    modal: {
      id: string;
      title: string;
      content?: string;
      componentName?: string;
      props?: Record<string, any>;
      size?: "sm" | "md" | "lg" | "xl" | "full";
    }
  ): Promise<void> {
    await this.route({
      type: "open_modal",
      payload: modal,
      target: {
        organizationId,
        userId,
      },
    });
  }

  async closeModal(
    organizationId: string,
    userId: string,
    modalId: string
  ): Promise<void> {
    await this.route({
      type: "close_modal",
      payload: {
        id: modalId,
      },
      target: {
        organizationId,
        userId,
      },
    });
  }
}
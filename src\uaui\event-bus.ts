import { Injectable, Logger } from "@nestjs/common";
import { ApixService } from "../apix/apix.service";
import { PrismaService } from "../prisma/prisma.service";
import { AnalyticsService } from "../analytics/analytics.service";
import { Subject, Observable } from "rxjs";
import { filter } from "rxjs/operators";

export interface EventData {
  type: string;
  payload: Record<string, any>;
  metadata?: Record<string, any>;
  organizationId: string;
  userId?: string;
  sessionId?: string;
  timestamp?: string;
}

@Injectable()
export class EventBus {
  private readonly logger = new Logger(EventBus.name);
  private eventSubject = new Subject<EventData>();

  constructor(
    private apixService: ApixService,
    private prisma: PrismaService,
    private analyticsService: AnalyticsService
  ) {}

  async emit(event: EventData): Promise<void> {
    try {
      // Add timestamp if not provided
      const eventWithTimestamp = {
        ...event,
        timestamp: event.timestamp || new Date().toISOString(),
      };

      // Emit to APIX for WebSocket clients
      await this.apixService.emit({
        type: event.type,
        payload: event.payload,
        organizationId: event.organizationId,
        userId: event.userId,
        sessionId: event.sessionId,
        metadata: event.metadata,
      });

      // Emit to local subscribers
      this.eventSubject.next(eventWithTimestamp);

      // Track analytics
      await this.analyticsService.track("event_bus_emit", {
        eventType: event.type,
        organizationId: event.organizationId,
        userId: event.userId,
        sessionId: event.sessionId,
        hasPayload: Object.keys(event.payload || {}).length > 0,
      });

      // Store event in database for replay/history
      await this.prisma.analyticsEvent.create({
        data: {
          type: event.type,
          payload: event.payload,
          organizationId: event.organizationId,
          userId: event.userId,
          sessionId: event.sessionId,
          metadata: event.metadata || {},
          timestamp: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Error emitting event: ${error.message}`, error.stack);
    }
  }

  on(eventType: string): Observable<EventData> {
    return this.eventSubject.pipe(
      filter((event) => event.type === eventType)
    );
  }

  onAny(): Observable<EventData> {
    return this.eventSubject.asObservable();
  }

  onMany(eventTypes: string[]): Observable<EventData> {
    return this.eventSubject.pipe(
      filter((event) => eventTypes.includes(event.type))
    );
  }

  onForOrganization(
    organizationId: string,
    eventType?: string
  ): Observable<EventData> {
    return this.eventSubject.pipe(
      filter((event) => {
        const orgMatch = event.organizationId === organizationId;
        return eventType ? orgMatch && event.type === eventType : orgMatch;
      })
    );
  }

  onForUser(userId: string, eventType?: string): Observable<EventData> {
    return this.eventSubject.pipe(
      filter((event) => {
        const userMatch = event.userId === userId;
        return eventType ? userMatch && event.type === eventType : userMatch;
      })
    );
  }

  onForSession(sessionId: string, eventType?: string): Observable<EventData> {
    return this.eventSubject.pipe(
      filter((event) => {
        const sessionMatch = event.sessionId === sessionId;
        return eventType
          ? sessionMatch && event.type === eventType
          : sessionMatch;
      })
    );
  }

  async getEventHistory(
    organizationId: string,
    filters: {
      eventType?: string;
      userId?: string;
      sessionId?: string;
      startDate?: Date;
      endDate?: Date;
      limit?: number;
    }
  ): Promise<EventData[]> {
    const { eventType, userId, sessionId, startDate, endDate, limit = 100 } =
      filters;

    const events = await this.prisma.analyticsEvent.findMany({
      where: {
        organizationId,
        type: eventType,
        userId,
        sessionId,
        timestamp: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        timestamp: "desc",
      },
      take: limit,
    });

    return events.map((event) => ({
      type: event.type,
      payload: event.payload as Record<string, any>,
      metadata: event.metadata as Record<string, any>,
      organizationId: event.organizationId,
      userId: event.userId,
      sessionId: event.sessionId,
      timestamp: event.timestamp.toISOString(),
    }));
  }
}
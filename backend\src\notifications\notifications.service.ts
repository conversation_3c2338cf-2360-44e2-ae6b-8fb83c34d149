import { Injectable } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";

@Injectable()
export class NotificationsService {
  constructor(private prisma: PrismaService) {}

  async send(
    organizationId: string,
    userId: string,
    type: string,
    title: string,
    message: string,
    data?: Record<string, any>,
  ): Promise<void> {
    try {
      await this.prisma.notification.create({
        data: {
          organizationId,
          userId,
          type,
          title,
          message,
          data: data || {},
          isRead: false,
          createdAt: new Date(),
        },
      });

      // Here you would integrate with actual notification services
      // like email, SMS, push notifications, etc.
      console.log(`Notification sent: ${title} to user ${userId}`);
    } catch (error) {
      console.error("Notification sending error:", error);
    }
  }

  async getNotifications(
    organizationId: string,
    userId?: string,
    isRead?: boolean,
    limit: number = 50,
  ) {
    const where: any = {
      organizationId,
    };

    if (userId) {
      where.userId = userId;
    }

    if (isRead !== undefined) {
      where.isRead = isRead;
    }

    return this.prisma.notification.findMany({
      where,
      take: limit,
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  async markAsRead(notificationId: string, userId: string): Promise<void> {
    await this.prisma.notification.updateMany({
      where: {
        id: notificationId,
        userId,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
  }

  async markAllAsRead(organizationId: string, userId: string): Promise<void> {
    await this.prisma.notification.updateMany({
      where: {
        organizationId,
        userId,
        isRead: false,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
  }
}

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from "@nestjs/common";
import { AgentsService } from "./agents.service";
import { CreateAgentDto } from "./dto/create-agent.dto";
import { UpdateAgentDto } from "./dto/update-agent.dto";
import { QueryAgentsDto } from "./dto/query-agents.dto";
import { ExecuteAgentDto } from "./dto/execute-agent.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { AuthenticatedRequest } from "../common/middleware/tenant.middleware";

@Controller("agents")
@UseGuards(JwtAuthGuard)
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Post()
  async create(
    @Body(ValidationPipe) createAgentDto: CreateAgentDto,
    @Request() req: AuthenticatedRequest,
  ) {
    const agent = await this.agentsService.create(createAgentDto, req.user);
    return {
      success: true,
      data: agent,
      message: "Agent created successfully",
      timestamp: new Date().toISOString(),
    };
  }

  @Get()
  async findAll(
    @Query(ValidationPipe) queryDto: QueryAgentsDto,
    @Request() req: AuthenticatedRequest,
  ) {
    const result = await this.agentsService.findAll(queryDto, req.user);
    return {
      success: true,
      ...result,
      timestamp: new Date().toISOString(),
    };
  }

  @Get("stats")
  async getStats(@Request() req: AuthenticatedRequest) {
    const stats = await this.agentsService.getStats(req.user);
    return {
      success: true,
      data: stats,
      timestamp: new Date().toISOString(),
    };
  }

  @Get(":id")
  async findOne(
    @Param("id", ParseUUIDPipe) id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    const agent = await this.agentsService.findOne(id, req.user);
    return {
      success: true,
      data: agent,
      timestamp: new Date().toISOString(),
    };
  }

  @Patch(":id")
  async update(
    @Param("id", ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateAgentDto: UpdateAgentDto,
    @Request() req: AuthenticatedRequest,
  ) {
    const agent = await this.agentsService.update(id, updateAgentDto, req.user);
    return {
      success: true,
      data: agent,
      message: "Agent updated successfully",
      timestamp: new Date().toISOString(),
    };
  }

  @Delete(":id")
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Param("id", ParseUUIDPipe) id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    await this.agentsService.remove(id, req.user);
  }

  @Post(":id/execute")
  async execute(
    @Param("id", ParseUUIDPipe) id: string,
    @Body(ValidationPipe) executeDto: ExecuteAgentDto,
    @Request() req: AuthenticatedRequest,
  ) {
    const result = await this.agentsService.execute(id, executeDto, req.user);
    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    };
  }

  @Get(":id/executions")
  async getExecutions(
    @Param("id", ParseUUIDPipe) id: string,
    @Query("page") page: number = 1,
    @Query("limit") limit: number = 10,
    @Request() req: AuthenticatedRequest,
  ) {
    const result = await this.agentsService.getExecutions(
      id,
      req.user,
      page,
      limit,
    );
    return {
      success: true,
      ...result,
      timestamp: new Date().toISOString(),
    };
  }

  @Post(":id/duplicate")
  async duplicate(
    @Param("id", ParseUUIDPipe) id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    const agent = await this.agentsService.duplicate(id, req.user);
    return {
      success: true,
      data: agent,
      message: "Agent duplicated successfully",
      timestamp: new Date().toISOString(),
    };
  }
}

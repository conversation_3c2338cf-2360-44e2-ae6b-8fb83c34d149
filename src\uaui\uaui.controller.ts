import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  Param,
  Query,
  HttpException,
  HttpStatus,
} from "@nestjs/common";
import { UAUIService, UAUIRequest, UAUIResponse } from "./uaui.service";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { AuthenticatedRequest } from "../common/middleware/tenant.middleware";
import { SmartProviderSelector } from "./smart-provider-selector";
import { StateManager } from "./state-manager";
import { EventBus } from "./event-bus";
import { RouterEngine } from "./router-engine";

@Controller("api/v1/uaui")
@UseGuards(JwtAuthGuard)
export class UAUIController {
  constructor(
    private readonly uauiService: UAUIService,
    private readonly smartProviderSelector: SmartProviderSelector,
    private readonly stateManager: StateManager,
    private readonly eventBus: EventBus,
    private readonly routerEngine: RouterEngine
  ) {}

  @Post("process")
  async processRequest(
    @Body() requestData: Omit<UAUIRequest, "userId" | "organizationId">,
    @Req() req: AuthenticatedRequest
  ): Promise<UAUIResponse> {
    try {
      const fullRequest: UAUIRequest = {
        ...requestData,
        userId: req.user.id,
        organizationId: req.user.organizationId,
      };

      return await this.uauiService.processRequest(fullRequest);
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to process request",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get("session/:sessionId/state")
  async getSessionState(
    @Param("sessionId") sessionId: string,
    @Req() req: AuthenticatedRequest
  ) {
    try {
      // TODO: Add permission check to ensure user has access to this session
      const state = await this.stateManager.getSessionState(sessionId);
      return { success: true, data: state };
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to get session state",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post("session/:sessionId/state")
  async updateSessionState(
    @Param("sessionId") sessionId: string,
    @Body() updates: Record<string, any>,
    @Req() req: AuthenticatedRequest
  ) {
    try {
      // TODO: Add permission check to ensure user has access to this session
      await this.stateManager.updateSessionState(sessionId, updates);
      return { success: true };
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to update session state",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get("events")
  async getEventHistory(
    @Query("type") eventType: string,
    @Query("userId") userId: string,
    @Query("sessionId") sessionId: string,
    @Query("startDate") startDate: string,
    @Query("endDate") endDate: string,
    @Query("limit") limit: number,
    @Req() req: AuthenticatedRequest
  ) {
    try {
      const events = await this.eventBus.getEventHistory(
        req.user.organizationId,
        {
          eventType,
          userId,
          sessionId,
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          limit: limit ? Number(limit) : 100,
        }
      );
      return { success: true, data: events };
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to get event history",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post("route")
  async routeCommand(
    @Body()
    command: {
      type: string;
      payload: Record<string, any>;
      targetAppId?: string;
      targetUserId?: string;
      targetSessionId?: string;
    },
    @Req() req: AuthenticatedRequest
  ) {
    try {
      await this.routerEngine.route({
        type: command.type,
        payload: command.payload,
        target: {
          appId: command.targetAppId,
          userId: command.targetUserId,
          sessionId: command.targetSessionId,
          organizationId: req.user.organizationId,
        },
      });
      return { success: true };
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to route command",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post("provider/select")
  async selectProvider(
    @Body()
    criteria: {
      model?: string;
      task?: string;
      maxLatency?: number;
      minReliability?: number;
      maxCost?: number;
      capabilities?: string[];
      context?: Record<string, any>;
    },
    @Req() req: AuthenticatedRequest
  ) {
    try {
      const provider = await this.smartProviderSelector.selectProvider(
        req.user.organizationId,
        criteria
      );
      return {
        success: true,
        data: {
          id: provider.id,
          name: provider.name,
          type: provider.type,
        },
      };
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to select provider",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
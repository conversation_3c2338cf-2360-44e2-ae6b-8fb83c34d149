import { Injectable, Logger } from "@nestjs/common";
import { ApixGateway } from "./apix.gateway";
import { PrismaService } from "../prisma/prisma.service";
import { BillingService } from "../billing/billing.service";
import { AnalyticsService } from "../analytics/analytics.service";

export interface ApixEvent {
  type: string;
  payload: Record<string, any>;
  organizationId: string;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class ApixService {
  private readonly logger = new Logger(ApixService.name);
  private eventQueue: ApixEvent[] = [];
  private isProcessing = false;

  constructor(
    private apixGateway: ApixGateway,
    private prisma: PrismaService,
    private billingService: BillingService,
    private analyticsService: AnalyticsService,
  ) {
    // Start event processing
    this.startEventProcessor();
  }

  async emit(event: ApixEvent): Promise<void> {
    try {
      // Add to queue for processing
      this.eventQueue.push({
        ...event,
        metadata: {
          ...event.metadata,
          timestamp: new Date().toISOString(),
          eventId: this.generateEventId(),
        },
      });

      // Immediate broadcast for real-time events
      if (this.isRealTimeEvent(event.type)) {
        await this.broadcastEvent(event);
      }
    } catch (error) {
      this.logger.error("Failed to emit APIX event:", error);
    }
  }

  async broadcastEvent(event: ApixEvent): Promise<void> {
    const { type, payload, organizationId, userId } = event;

    // Broadcast to organization
    await this.apixGateway.broadcastToOrganization(
      organizationId,
      type,
      payload,
    );

    // Broadcast to specific user if specified
    if (userId) {
      await this.apixGateway.broadcastToUser(userId, type, payload);
    }

    // Broadcast to specific channels based on event type
    const channels = this.getEventChannels(type, organizationId);
    for (const channel of channels) {
      await this.apixGateway.broadcastToChannel(channel, type, payload);
    }
  }

  // Agent Events
  async emitAgentEvent(
    type:
      | "agent_created"
      | "agent_updated"
      | "agent_executed"
      | "agent_execution_started"
      | "agent_execution_completed"
      | "agent_execution_failed",
    agentId: string,
    organizationId: string,
    userId: string,
    payload: Record<string, any> = {},
  ): Promise<void> {
    await this.emit({
      type,
      payload: {
        agentId,
        ...payload,
      },
      organizationId,
      userId,
    });
  }

  // Tool Events
  async emitToolEvent(
    type:
      | "tool_created"
      | "tool_updated"
      | "tool_executed"
      | "tool_execution_started"
      | "tool_execution_completed"
      | "tool_execution_failed",
    toolId: string,
    organizationId: string,
    userId: string,
    payload: Record<string, any> = {},
  ): Promise<void> {
    await this.emit({
      type,
      payload: {
        toolId,
        ...payload,
      },
      organizationId,
      userId,
    });
  }

  // Provider Events
  async emitProviderEvent(
    type:
      | "provider_selected"
      | "provider_switched"
      | "provider_failed"
      | "provider_optimized",
    providerId: string,
    organizationId: string,
    payload: Record<string, any> = {},
  ): Promise<void> {
    await this.emit({
      type,
      payload: {
        providerId,
        ...payload,
      },
      organizationId,
    });
  }

  // Session Events
  async emitSessionEvent(
    type:
      | "session_created"
      | "session_updated"
      | "session_message"
      | "session_completed",
    sessionId: string,
    organizationId: string,
    userId: string,
    payload: Record<string, any> = {},
  ): Promise<void> {
    await this.emit({
      type,
      payload: {
        sessionId,
        ...payload,
      },
      organizationId,
      userId,
      sessionId,
    });
  }

  // HITL Events
  async emitHITLEvent(
    type:
      | "hitl_request_created"
      | "hitl_approved"
      | "hitl_rejected"
      | "hitl_escalated",
    requestId: string,
    organizationId: string,
    userId: string,
    payload: Record<string, any> = {},
  ): Promise<void> {
    await this.emit({
      type,
      payload: {
        requestId,
        ...payload,
      },
      organizationId,
      userId,
    });
  }

  // Billing Events
  async emitBillingEvent(
    type: "quota_exceeded" | "billing_updated" | "usage_warning",
    organizationId: string,
    payload: Record<string, any> = {},
  ): Promise<void> {
    await this.emit({
      type,
      payload,
      organizationId,
    });
  }

  // System Events
  async emitSystemEvent(
    type: "system_health" | "maintenance_mode" | "service_degraded",
    payload: Record<string, any> = {},
  ): Promise<void> {
    // System events are broadcast to all organizations
    const organizations = await this.prisma.organization.findMany({
      where: { status: "ACTIVE" },
      select: { id: true },
    });

    for (const org of organizations) {
      await this.emit({
        type,
        payload,
        organizationId: org.id,
      });
    }
  }

  private async startEventProcessor(): Promise<void> {
    setInterval(async () => {
      if (this.isProcessing || this.eventQueue.length === 0) {
        return;
      }

      this.isProcessing = true;
      const batchSize = 100;
      const batch = this.eventQueue.splice(0, batchSize);

      try {
        await this.processBatch(batch);
      } catch (error) {
        this.logger.error("Failed to process event batch:", error);
        // Re-queue failed events
        this.eventQueue.unshift(...batch);
      } finally {
        this.isProcessing = false;
      }
    }, 1000); // Process every second
  }

  private async processBatch(events: ApixEvent[]): Promise<void> {
    // Store events for replay and analytics
    const eventRecords = events.map((event) => ({
      type: event.type,
      payload: event.payload,
      organizationId: event.organizationId,
      userId: event.userId,
      sessionId: event.sessionId,
      metadata: event.metadata || {},
      timestamp: new Date(),
    }));

    await this.prisma.analyticsEvent.createMany({
      data: eventRecords,
    });

    // Process non-real-time events
    for (const event of events) {
      if (!this.isRealTimeEvent(event.type)) {
        await this.broadcastEvent(event);
      }

      // Track billing for certain events
      if (this.isBillableEvent(event.type)) {
        await this.billingService.trackUsage(
          event.organizationId,
          "apix_events",
          1,
          0.0001, // Small cost per event
          { eventType: event.type },
        );
      }
    }
  }

  private isRealTimeEvent(eventType: string): boolean {
    const realTimeEvents = [
      "agent_execution_started",
      "agent_execution_completed",
      "tool_execution_started",
      "tool_execution_completed",
      "session_message",
      "hitl_request_created",
      "quota_exceeded",
    ];
    return realTimeEvents.includes(eventType);
  }

  private isBillableEvent(eventType: string): boolean {
    const billableEvents = [
      "agent_executed",
      "tool_executed",
      "provider_selected",
      "session_created",
    ];
    return billableEvents.includes(eventType);
  }

  private getEventChannels(
    eventType: string,
    organizationId: string,
  ): string[] {
    const channels: string[] = [];

    // Agent channels
    if (eventType.startsWith("agent_")) {
      channels.push(`org:${organizationId}:agents`);
    }

    // Tool channels
    if (eventType.startsWith("tool_")) {
      channels.push(`org:${organizationId}:tools`);
    }

    // Provider channels
    if (eventType.startsWith("provider_")) {
      channels.push(`org:${organizationId}:providers`);
    }

    // Session channels
    if (eventType.startsWith("session_")) {
      channels.push(`org:${organizationId}:sessions`);
    }

    // HITL channels
    if (eventType.startsWith("hitl_")) {
      channels.push(`org:${organizationId}:hitl`);
    }

    // Billing channels
    if (eventType.startsWith("billing_") || eventType.includes("quota")) {
      channels.push(`org:${organizationId}:billing`);
    }

    return channels;
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Health check
  getHealthStatus() {
    return {
      connected_clients: this.apixGateway.getConnectedClientsCount(),
      queue_size: this.eventQueue.length,
      is_processing: this.isProcessing,
      timestamp: new Date().toISOString(),
    };
  }
}

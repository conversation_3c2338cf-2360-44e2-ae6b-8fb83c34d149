import { Module } from "@nestjs/common";
import { UAUIService } from "./uaui.service";
import { UAUIController } from "./uaui.controller";
import { PrismaModule } from "../prisma/prisma.module";
import { ProvidersModule } from "../providers/providers.module";
import { ToolsModule } from "../tools/tools.module";
import { AgentsModule } from "../agents/agents.module";
import { SessionsModule } from "../sessions/sessions.module";
import { ApixModule } from "../apix/apix.module";
import { BillingModule } from "../billing/billing.module";
import { AnalyticsModule } from "../analytics/analytics.module";
import { SmartProviderSelector } from "./smart-provider-selector";
import { StateManager } from "./state-manager";
import { EventBus } from "./event-bus";
import { RouterEngine } from "./router-engine";

@Module({
  imports: [
    PrismaModule,
    ProvidersModule,
    ToolsModule,
    AgentsModule,
    SessionsModule,
    ApixModule,
    BillingModule,
    AnalyticsModule,
  ],
  controllers: [UAUIController],
  providers: [
    UAUIService,
    SmartProviderSelector,
    StateManager,
    EventBus,
    RouterEngine,
  ],
  exports: [UAUIService, SmartProviderSelector, StateManager, EventBus, RouterEngine],
})
export class UAUIModule {}
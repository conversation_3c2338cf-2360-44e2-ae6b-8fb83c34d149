import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { BarChart, Activity, DollarSign, AlertCircle } from "lucide-react";

interface ResourceMetric {
  name: string;
  value: number;
  max: number;
  percentage: number;
  icon: React.ReactNode;
}

interface CostBreakdown {
  category: string;
  amount: number;
  percentage: number;
}

interface ResourceManagementProps {
  quotaMetrics?: ResourceMetric[];
  costBreakdown?: CostBreakdown[];
  performanceMetrics?: ResourceMetric[];
}

const ResourceManagement = ({
  quotaMetrics = [
    {
      name: "Agent Executions",
      value: 8750,
      max: 10000,
      percentage: 87.5,
      icon: <Activity className="h-4 w-4 text-blue-500" />,
    },
    {
      name: "Tool Calls",
      value: 4200,
      max: 5000,
      percentage: 84,
      icon: <BarChart className="h-4 w-4 text-green-500" />,
    },
    {
      name: "Storage Used",
      value: 7.5,
      max: 10,
      percentage: 75,
      icon: <AlertCircle className="h-4 w-4 text-yellow-500" />,
    },
  ],
  costBreakdown = [
    { category: "Agent Executions", amount: 125.5, percentage: 45 },
    { category: "Tool Calls", amount: 78.25, percentage: 28 },
    { category: "Knowledge Base", amount: 42.75, percentage: 15 },
    { category: "Storage", amount: 33.5, percentage: 12 },
  ],
  performanceMetrics = [
    {
      name: "Agent Response Time",
      value: 850,
      max: 2000,
      percentage: 42.5,
      icon: <Activity className="h-4 w-4 text-blue-500" />,
    },
    {
      name: "Tool Execution Time",
      value: 320,
      max: 1000,
      percentage: 32,
      icon: <BarChart className="h-4 w-4 text-green-500" />,
    },
    {
      name: "Workflow Completion",
      value: 95,
      max: 100,
      percentage: 95,
      icon: <AlertCircle className="h-4 w-4 text-yellow-500" />,
    },
  ],
}: ResourceManagementProps) => {
  const totalCost = costBreakdown.reduce((sum, item) => sum + item.amount, 0);

  return (
    <div className="w-full h-full bg-background p-4 rounded-lg">
      <h2 className="text-2xl font-bold mb-4">Resource Management</h2>

      <Tabs defaultValue="quota" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="quota">Quota Usage</TabsTrigger>
          <TabsTrigger value="billing">Billing Information</TabsTrigger>
          <TabsTrigger value="performance">Performance Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="quota" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quota Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {quotaMetrics.map((metric, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {metric.icon}
                        <span className="text-sm font-medium">
                          {metric.name}
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {metric.value.toLocaleString()} /{" "}
                        {metric.max.toLocaleString()}
                      </span>
                    </div>
                    <Progress value={metric.percentage} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quota Alerts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md bg-yellow-50 p-4 border border-yellow-200">
                <div className="flex items-center gap-3">
                  <AlertCircle className="h-5 w-5 text-yellow-600" />
                  <div>
                    <h4 className="font-medium text-yellow-800">
                      Approaching Quota Limit
                    </h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      You've used 87.5% of your Agent Executions quota for this
                      month. Consider upgrading your plan.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Current Billing Period</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-6">
                <div>
                  <p className="text-sm text-muted-foreground">
                    Current Period
                  </p>
                  <p className="font-medium">May 1 - May 31, 2023</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-muted-foreground">Total Cost</p>
                  <p className="text-2xl font-bold">${totalCost.toFixed(2)}</p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Cost Breakdown</h4>
                {costBreakdown.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-green-500" />
                      <span className="text-sm">{item.category}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="w-32 h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-green-500"
                          style={{ width: `${item.percentage}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium">
                        ${item.amount.toFixed(2)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { period: "April 2023", amount: 255.75, status: "Paid" },
                  { period: "March 2023", amount: 232.5, status: "Paid" },
                  { period: "February 2023", amount: 198.25, status: "Paid" },
                ].map((invoice, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between py-2 border-b last:border-0"
                  >
                    <span className="text-sm">{invoice.period}</span>
                    <div className="flex items-center gap-4">
                      <span className="text-sm font-medium">
                        ${invoice.amount.toFixed(2)}
                      </span>
                      <span className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">
                        {invoice.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {performanceMetrics.map((metric, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {metric.icon}
                        <span className="text-sm font-medium">
                          {metric.name}
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {metric.name.includes("Time")
                          ? `${metric.value}ms`
                          : `${metric.value}%`}
                      </span>
                    </div>
                    <Progress value={metric.percentage} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Health</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { name: "API Uptime", value: "99.99%", status: "healthy" },
                  { name: "Database", value: "98.5%", status: "healthy" },
                  { name: "WebSocket", value: "99.95%", status: "healthy" },
                ].map((service, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      {service.name}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <p className="font-medium">{service.value}</p>
                      <span className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">
                        {service.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ResourceManagement;

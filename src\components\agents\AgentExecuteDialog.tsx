"use client";

import React, { useState } from "react";
import { Agent } from "../../../shared/types";
import { apiClient } from "@/lib/api";
import { useMutation } from "@/hooks/useApi";
import { useToast } from "@/components/ui/use-toast";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Play,
  ChevronDown,
  ChevronRight,
  Settings,
  Loader,
  CheckCircle,
  XCircle,
} from "lucide-react";

interface AgentExecuteDialogProps {
  agent: Agent | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const AgentExecuteDialog = ({ agent, open, onOpenChange }: AgentExecuteDialogProps) => {
  const { toast } = useToast();
  const [input, setInput] = useState("");
  const [sessionId, setSessionId] = useState("");
  const [temperature, setTemperature] = useState<number | undefined>(undefined);
  const [maxTokens, setMaxTokens] = useState<number | undefined>(undefined);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [executionResult, setExecutionResult] = useState<any>(null);

  const { mutate: executeAgent, loading } = useMutation(
    async (data: {
      input: string;
      sessionId?: string;
      temperature?: number;
      maxTokens?: number;
    }) => {
      if (!agent) throw new Error("No agent selected");
      return await apiClient.executeAgent(agent.id, data);
    },
    {
      onSuccess: (response) => {
        setExecutionResult(response.data);
        toast({
          title: "Execution started",
          description: `Agent execution has been initiated successfully.`,
        });
      },
      onError: (error) => {
        toast({
          title: "Execution failed",
          description: error.message,
          variant: "destructive",
        });
      },
    }
  );

  const handleExecute = () => {
    if (!input.trim()) {
      toast({
        title: "Input required",
        description: "Please provide input for the agent to process.",
        variant: "destructive",
      });
      return;
    }

    const executionData: any = { input: input.trim() };
    
    if (sessionId.trim()) {
      executionData.sessionId = sessionId.trim();
    }
    
    if (temperature !== undefined && temperature !== agent?.temperature) {
      executionData.temperature = temperature;
    }
    
    if (maxTokens !== undefined && maxTokens !== agent?.maxTokens) {
      executionData.maxTokens = maxTokens;
    }

    executeAgent(executionData);
  };

  const handleClose = () => {
    setInput("");
    setSessionId("");
    setTemperature(undefined);
    setMaxTokens(undefined);
    setExecutionResult(null);
    setShowAdvanced(false);
    onOpenChange(false);
  };

  const resetForm = () => {
    setInput("");
    setSessionId("");
    setTemperature(agent?.temperature);
    setMaxTokens(agent?.maxTokens);
    setExecutionResult(null);
  };

  React.useEffect(() => {
    if (agent && open) {
      setTemperature(agent.temperature);
      setMaxTokens(agent.maxTokens);
    }
  }, [agent, open]);

  if (!agent) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Play className="h-5 w-5 text-blue-500" />
            Execute Agent: {agent.name}
          </DialogTitle>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline">{agent.model}</Badge>
            <Badge className="bg-green-100 text-green-800 border-green-200">
              {agent.status}
            </Badge>
          </div>
        </DialogHeader>

        <ScrollArea className="max-h-[60vh] pr-4">
          <div className="space-y-4">
            {/* Input Section */}
            <div className="space-y-2">
              <Label htmlFor="input">Input *</Label>
              <Textarea
                id="input"
                placeholder="Enter your message or prompt for the agent..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                className="min-h-[100px]"
                disabled={loading}
              />
            </div>

            {/* Session ID */}
            <div className="space-y-2">
              <Label htmlFor="sessionId">Session ID (Optional)</Label>
              <Input
                id="sessionId"
                placeholder="Enter session ID to continue existing conversation"
                value={sessionId}
                onChange={(e) => setSessionId(e.target.value)}
                disabled={loading}
              />
            </div>

            {/* Advanced Settings */}
            <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-start p-0">
                  {showAdvanced ? (
                    <ChevronDown className="h-4 w-4 mr-2" />
                  ) : (
                    <ChevronRight className="h-4 w-4 mr-2" />
                  )}
                  <Settings className="h-4 w-4 mr-2" />
                  Advanced Settings
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-4 mt-4">
                <Separator />
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="temperature">Temperature</Label>
                    <Input
                      id="temperature"
                      type="number"
                      min="0"
                      max="2"
                      step="0.1"
                      placeholder={agent.temperature.toString()}
                      value={temperature || ""}
                      onChange={(e) => setTemperature(parseFloat(e.target.value) || undefined)}
                      disabled={loading}
                    />
                    <p className="text-xs text-gray-500">
                      Default: {agent.temperature}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxTokens">Max Tokens</Label>
                    <Input
                      id="maxTokens"
                      type="number"
                      min="1"
                      max="32000"
                      placeholder={agent.maxTokens.toString()}
                      value={maxTokens || ""}
                      onChange={(e) => setMaxTokens(parseInt(e.target.value) || undefined)}
                      disabled={loading}
                    />
                    <p className="text-xs text-gray-500">
                      Default: {agent.maxTokens}
                    </p>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Execution Result */}
            {executionResult && (
              <div className="space-y-2">
                <Separator />
                <div className="flex items-center gap-2">
                  {executionResult.status === "RUNNING" ? (
                    <Loader className="h-4 w-4 text-blue-500 animate-spin" />
                  ) : executionResult.status === "COMPLETED" ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <Label>Execution Result</Label>
                </div>
                <div className="bg-gray-50 border rounded-md p-3">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">Execution ID:</span>
                      <span className="font-mono">{executionResult.executionId}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">Status:</span>
                      <Badge
                        className={
                          executionResult.status === "RUNNING"
                            ? "bg-blue-100 text-blue-800 border-blue-200"
                            : executionResult.status === "COMPLETED"
                            ? "bg-green-100 text-green-800 border-green-200"
                            : "bg-red-100 text-red-800 border-red-200"
                        }
                      >
                        {executionResult.status}
                      </Badge>
                    </div>
                    {executionResult.message && (
                      <div className="text-sm">
                        <span className="font-medium">Message:</span>
                        <p className="mt-1 text-gray-600">{executionResult.message}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={resetForm} disabled={loading}>
              Reset
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Close
            </Button>
            <Button onClick={handleExecute} disabled={loading || !input.trim()}>
              {loading ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Executing...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Execute
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AgentExecuteDialog;

import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(private prisma: PrismaService) {}

  async getSystemHealth() {
    const startTime = Date.now();

    try {
      // Check database connectivity
      const dbHealthy = await this.prisma.healthCheck();

      // Get system metrics
      const memoryUsage = process.memoryUsage();
      const uptime = process.uptime();
      const cpuUsage = process.cpuUsage();

      const responseTime = Date.now() - startTime;

      return {
        status: dbHealthy ? "healthy" : "unhealthy",
        timestamp: new Date().toISOString(),
        uptime: uptime,
        responseTime: responseTime,
        database: {
          status: dbHealthy ? "connected" : "disconnected",
        },
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          external: Math.round(memoryUsage.external / 1024 / 1024), // MB
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
        },
      };
    } catch (error) {
      this.logger.error("Health check failed:", error);
      return {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: error.message,
        responseTime: Date.now() - startTime,
      };
    }
  }

  async getDatabaseStats() {
    try {
      const stats = await this.prisma.getDatabaseStats();
      return {
        status: "success",
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error("Failed to get database stats:", error);
      return {
        status: "error",
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}

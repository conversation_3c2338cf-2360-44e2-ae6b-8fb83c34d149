import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ThrottlerModule } from "@nestjs/throttler";
import { BullModule } from "@nestjs/bull";
import { RedisModule } from "@nestjs/redis";
import { PrismaModule } from "./prisma/prisma.module";
import { AuthModule } from "./auth/auth.module";
import { UsersModule } from "./users/users.module";
import { OrganizationsModule } from "./organizations/organizations.module";
import { AgentsModule } from "./agents/agents.module";
import { ToolsModule } from "./tools/tools.module";
import { HybridsModule } from "./hybrids/hybrids.module";
import { SessionsModule } from "./sessions/sessions.module";
import { ProvidersModule } from "./providers/providers.module";
import { KnowledgeModule } from "./knowledge/knowledge.module";
import { WidgetsModule } from "./widgets/widgets.module";
import { AnalyticsModule } from "./analytics/analytics.module";
import { NotificationsModule } from "./notifications/notifications.module";
import { BillingModule } from "./billing/billing.module";
import { ApixModule } from "./apix/apix.module";
import { HealthModule } from "./health/health.module";
import { UAUIModule } from "./uaui/uaui.module";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env.local", ".env"],
    }),
    ThrottlerModule.forRoot([
      {
        ttl: 60000,
        limit: 100,
      },
    ]),
    RedisModule.forRoot({
      config: {
        host: process.env.REDIS_HOST || "localhost",
        port: parseInt(process.env.REDIS_PORT) || 6379,
      },
    }),
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || "localhost",
        port: parseInt(process.env.REDIS_PORT) || 6379,
      },
    }),
    PrismaModule,
    AuthModule,
    UsersModule,
    OrganizationsModule,
    AgentsModule,
    ToolsModule,
    HybridsModule,
    SessionsModule,
    ProvidersModule,
    KnowledgeModule,
    WidgetsModule,
    AnalyticsModule,
    NotificationsModule,
    BillingModule,
    ApixModule,
    HealthModule,
    UAUIModule,
  ],
})
export class AppModule {}
import { ApiResponse, PaginatedResponse, Agent, AgentStatus } from "../../shared/types";

// Mock data for development when backend is not available
export const mockApiClient = {
  async getAgents(params?: any): Promise<PaginatedResponse<Agent>> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const mockAgents: Agent[] = [
      {
        id: "1",
        name: "Customer Support Agent",
        description: "Handles customer inquiries and support tickets",
        prompt: "You are a helpful customer support agent...",
        model: "gpt-4",
        temperature: 0.7,
        maxTokens: 1000,
        systemPrompt: "Be helpful and professional",
        status: AgentStatus.ACTIVE,
        isPublic: false,
        tags: ["support", "customer"],
        organizationId: "org-1",
        createdAt: new Date("2024-01-15").toISOString(),
        updatedAt: new Date("2024-01-15").toISOString(),
      },
      {
        id: "2",
        name: "Content Writer",
        description: "Creates engaging content for marketing",
        prompt: "You are a creative content writer...",
        model: "gpt-3.5-turbo",
        temperature: 0.8,
        maxTokens: 2000,
        systemPrompt: "Be creative and engaging",
        status: AgentStatus.ACTIVE,
        isPublic: true,
        tags: ["content", "marketing"],
        organizationId: "org-1",
        createdAt: new Date("2024-01-10").toISOString(),
        updatedAt: new Date("2024-01-10").toISOString(),
      },
      {
        id: "3",
        name: "Data Analyst",
        description: "Analyzes data and provides insights",
        prompt: "You are a data analyst...",
        model: "claude-3-sonnet",
        temperature: 0.3,
        maxTokens: 1500,
        systemPrompt: "Be analytical and precise",
        status: AgentStatus.DRAFT,
        isPublic: false,
        tags: ["data", "analytics"],
        organizationId: "org-1",
        createdAt: new Date("2024-01-05").toISOString(),
        updatedAt: new Date("2024-01-05").toISOString(),
      }
    ];

    return {
      success: true,
      data: mockAgents,
      pagination: {
        page: 1,
        limit: 10,
        total: mockAgents.length,
        totalPages: 1
      },
      message: "Agents retrieved successfully"
    };
  },
  async getAgentStats(): Promise<ApiResponse<{
    byStatus: Record<string, number>;
    totalExecutions: number;
    recentExecutions: number;
  }>> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      success: true,
      data: {
        byStatus: {
          ACTIVE: 12,
          INACTIVE: 3,
          DRAFT: 5,
          ERROR: 1
        },
        totalExecutions: 1247,
        recentExecutions: 23
      },
      message: "Agent stats retrieved successfully"
    };
  },

  async getToolStats(): Promise<ApiResponse<{
    byStatus: Record<string, number>;
    totalExecutions: number;
    recentExecutions: number;
  }>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      success: true,
      data: {
        byStatus: {
          ACTIVE: 8,
          INACTIVE: 2,
          DRAFT: 3
        },
        totalExecutions: 856,
        recentExecutions: 15
      },
      message: "Tool stats retrieved successfully"
    };
  },

  async getProviderStats(): Promise<ApiResponse<{
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    totalExecutions: number;
    recentlyUsed: number;
  }>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      success: true,
      data: {
        byType: {
          LLM: 5,
          EMBEDDING: 2,
          IMAGE: 1
        },
        byStatus: {
          ACTIVE: 6,
          INACTIVE: 2
        },
        totalExecutions: 2103,
        recentlyUsed: 38
      },
      message: "Provider stats retrieved successfully"
    };
  },

  async getTools(params?: any): Promise<PaginatedResponse<any>> {
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      data: [
        {
          id: "1",
          name: "Email Sender",
          description: "Sends emails via SMTP",
          category: "Communication",
          status: "ACTIVE",
          createdAt: new Date("2024-01-15").toISOString(),
        },
        {
          id: "2",
          name: "Data Processor",
          description: "Processes CSV data",
          category: "Data",
          status: "ACTIVE",
          createdAt: new Date("2024-01-10").toISOString(),
        }
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1
      },
      message: "Tools retrieved successfully"
    };
  },

  async getProviders(params?: any): Promise<PaginatedResponse<any>> {
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      data: [
        {
          id: "1",
          name: "OpenAI",
          type: "LLM",
          status: "ACTIVE",
          createdAt: new Date("2024-01-15").toISOString(),
        },
        {
          id: "2",
          name: "Anthropic",
          type: "LLM",
          status: "ACTIVE",
          createdAt: new Date("2024-01-10").toISOString(),
        }
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1
      },
      message: "Providers retrieved successfully"
    };
  }
};
